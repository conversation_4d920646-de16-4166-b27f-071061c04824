"""
Simple debug script to understand wells structure
"""

from cegalprizm.pythontool import PetrelConnection

def debug_wells_structure():
    """Debug the exact structure of petrel.wells"""
    
    try:
        petrel = PetrelConnection()
        print(f'Connected to: {petrel.get_current_project_name()}')
        
        print(f'\nType of petrel.wells: {type(petrel.wells)}')
        print(f'Length: {len(petrel.wells)}')
        
        # Check first few items in detail
        print('\nDetailed inspection of first 3 items:')
        
        wells_found = []
        
        for i, item in enumerate(petrel.wells):
            if i >= 3:  # Only check first 3
                break
                
            print(f'\n--- Item {i} ---')
            print(f'Type: {type(item)}')
            
            # Check if it's a list
            if isinstance(item, list):
                print(f'List length: {len(item)}')
                for j, sub_item in enumerate(item[:2]):  # Check first 2 sub-items
                    print(f'  Sub-item {j}: {type(sub_item)}')
                    if hasattr(sub_item, 'petrel_name'):
                        print(f'    Name: {sub_item.petrel_name}')
                        wells_found.append(sub_item)
                    else:
                        print(f'    No petrel_name')
            else:
                # Check if it has petrel_name
                if hasattr(item, 'petrel_name'):
                    print(f'Name: {item.petrel_name}')
                    wells_found.append(item)
                else:
                    print('No petrel_name attribute')
                    # Show available attributes
                    attrs = [attr for attr in dir(item) if not attr.startswith('_')]
                    print(f'Attributes: {attrs[:10]}')
        
        print(f'\nWells found so far: {len(wells_found)}')
        
        # Try different iteration approaches
        print('\n--- Testing different iteration methods ---')
        
        # Method 1: Direct iteration
        try:
            count = 0
            for well in petrel.wells:
                if hasattr(well, 'petrel_name'):
                    print(f'Direct iteration - Well {count}: {well.petrel_name}')
                    count += 1
                    if count >= 5:
                        break
            print(f'Direct iteration found {count} wells')
        except Exception as e:
            print(f'Direct iteration failed: {e}')
        
        # Method 2: Index access
        try:
            if len(petrel.wells) > 0:
                first_item = petrel.wells[0]
                print(f'First item type: {type(first_item)}')
                if hasattr(first_item, 'petrel_name'):
                    print(f'First item name: {first_item.petrel_name}')
                elif isinstance(first_item, list) and len(first_item) > 0:
                    print(f'First item is list with {len(first_item)} items')
                    if hasattr(first_item[0], 'petrel_name'):
                        print(f'First sub-item name: {first_item[0].petrel_name}')
        except Exception as e:
            print(f'Index access failed: {e}')
        
        return wells_found
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    print("Debugging wells structure...")
    wells = debug_wells_structure()
    print(f'\nFinal result: {len(wells)} wells found')
    if wells:
        for i, well in enumerate(wells[:5]):
            print(f'  {i+1}. {well.petrel_name}')
