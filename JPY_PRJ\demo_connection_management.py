"""
Demonstration of Petrel Connection Management
============================================

This script demonstrates proper Petrel connection management techniques
based on the patterns found in the pythontool directory.

Usage:
    python demo_connection_management.py

Features demonstrated:
- Direct connection creation and closing
- Context manager usage
- Connection status checking
- Error handling
- Interactive cleanup
"""

import sys
from cegalprizm.pythontool import PetrelConnection
from cegalprizm.pythontool.petrelconnection import make_connection


def demo_direct_connection():
    """Demonstrate direct connection management."""
    print("\n" + "="*60)
    print("DEMO 1: DIRECT CONNECTION MANAGEMENT")
    print("="*60)
    
    petrel = None
    try:
        # Create connection
        print("Creating Petrel connection...")
        petrel = PetrelConnection()
        
        # Test connection
        project_name = petrel.get_current_project_name()
        print(f"✓ Connected to project: {project_name}")
        
        # Ping to verify connection is active
        ping_result = petrel.ping()
        print(f"✓ Connection ping successful: {ping_result}")
        
        # Do some work (example: count wells)
        wells = list(petrel.wells)
        print(f"✓ Found {len(wells)} wells in the project")
        
    except Exception as e:
        print(f"✗ Error during connection operations: {e}")
        return False
        
    finally:
        # Always close the connection
        if petrel:
            try:
                print("Closing Petrel connection...")
                petrel.close()
                print("✓ Connection closed successfully")
            except Exception as e:
                print(f"⚠ Warning: Error closing connection: {e}")
    
    return True


def demo_context_manager():
    """Demonstrate context manager connection management."""
    print("\n" + "="*60)
    print("DEMO 2: CONTEXT MANAGER CONNECTION")
    print("="*60)
    
    try:
        # Use context manager for automatic cleanup
        with make_connection() as petrel:
            print("✓ Connection established via context manager")
            
            # Test connection
            project_name = petrel.get_current_project_name()
            print(f"✓ Connected to project: {project_name}")
            
            # Do some work
            wells = list(petrel.wells)
            print(f"✓ Found {len(wells)} wells in the project")
            
            # Connection will be automatically closed when exiting this block
            print("✓ Work completed - connection will auto-close")
            
        print("✓ Context manager automatically closed the connection")
        return True
        
    except Exception as e:
        print(f"✗ Error during context manager operations: {e}")
        return False


def check_connection_status(petrel):
    """Check and display connection status."""
    try:
        # Try to ping the connection
        ping_result = petrel.ping()
        project_name = petrel.get_current_project_name()
        print(f"✓ Connection is active (Project: {project_name}, Ping: {ping_result})")
        return True
    except Exception as e:
        print(f"✗ Connection appears to be inactive: {e}")
        return False


def demo_connection_monitoring():
    """Demonstrate connection status monitoring."""
    print("\n" + "="*60)
    print("DEMO 3: CONNECTION STATUS MONITORING")
    print("="*60)
    
    petrel = None
    try:
        # Create connection
        print("Creating connection...")
        petrel = PetrelConnection()
        
        # Check status after creation
        print("Checking status after creation:")
        check_connection_status(petrel)
        
        # Do some operations
        print("\nPerforming operations...")
        wells = list(petrel.wells)
        print(f"Processed {len(wells)} wells")
        
        # Check status again
        print("\nChecking status after operations:")
        check_connection_status(petrel)
        
        # Close connection
        print("\nClosing connection...")
        petrel.close()
        
        # Try to check status after closing (should fail)
        print("\nChecking status after closing:")
        check_connection_status(petrel)
        
        return True
        
    except Exception as e:
        print(f"✗ Error during monitoring demo: {e}")
        return False


def interactive_demo():
    """Interactive demonstration with user choices."""
    print("\n" + "="*60)
    print("DEMO 4: INTERACTIVE CONNECTION MANAGEMENT")
    print("="*60)
    
    print("Choose a connection management method:")
    print("1. Direct connection (manual management)")
    print("2. Context manager (automatic management)")
    print("3. Connection monitoring demo")
    print("4. Run all demos")
    
    try:
        choice = input("Enter your choice (1-4): ").strip()
        
        if choice == "1":
            return demo_direct_connection()
        elif choice == "2":
            return demo_context_manager()
        elif choice == "3":
            return demo_connection_monitoring()
        elif choice == "4":
            print("\nRunning all demos...")
            result1 = demo_direct_connection()
            result2 = demo_context_manager()
            result3 = demo_connection_monitoring()
            return all([result1, result2, result3])
        else:
            print("Invalid choice. Running all demos...")
            result1 = demo_direct_connection()
            result2 = demo_context_manager()
            result3 = demo_connection_monitoring()
            return all([result1, result2, result3])
            
    except (EOFError, KeyboardInterrupt):
        print("\nDemo interrupted by user.")
        return False


def main():
    """Main demonstration function."""
    print("PETREL CONNECTION MANAGEMENT DEMONSTRATION")
    print("="*60)
    print("This script demonstrates proper Petrel connection management")
    print("techniques based on the pythontool library patterns.")
    
    try:
        # Run interactive demo
        success = interactive_demo()
        
        print("\n" + "="*60)
        print("DEMONSTRATION SUMMARY")
        print("="*60)
        
        if success:
            print("✓ All demonstrations completed successfully!")
            print("\nKey takeaways:")
            print("• Always close connections when done")
            print("• Use context managers for automatic cleanup")
            print("• Handle connection errors gracefully")
            print("• Monitor connection status when needed")
        else:
            print("⚠ Some demonstrations encountered issues.")
            print("Please check the output above for details.")
            
        print("\nFor more information, see:")
        print("• PETREL_CONNECTION_MANAGEMENT.md")
        print("• Logs_imputation_petrel_enhanced.py")
        
    except Exception as e:
        print(f"\n✗ Demonstration failed: {e}")
        print("Please ensure Petrel is running with an active project.")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
