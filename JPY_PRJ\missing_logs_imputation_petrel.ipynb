{"cells": [{"cell_type": "markdown", "id": "fbad4559", "metadata": {}, "source": ["# Missing Logs Imputation – **Petrel‑connected** workflow\n", "\n", "This notebook connects directly to the **currently open Petrel project** with Cegal Prizm Python Tool Pro, pulls the selected global well logs for every well, applies machine‑learning models to fill in missing values, and (optionally) writes the imputed logs back into Petrel.\n", "\n", "*Generated 2025-06-19*."]}, {"cell_type": "code", "execution_count": 3, "id": "1d4e5410", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'cegal.welltools'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 16\u001b[0m\n\u001b[0;32m     14\u001b[0m \u001b[38;5;66;03m# Plotting (optional)\u001b[39;00m\n\u001b[0;32m     15\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mplotly\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mexpress\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpx\u001b[39;00m\n\u001b[1;32m---> 16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m<PERSON>gal\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mwell<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mplotting\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m <PERSON><PERSON><PERSON>lotter \u001b[38;5;28;01mas\u001b[39;00m cwp\n\u001b[0;32m     18\u001b[0m \u001b[38;5;66;03m# Petrel connection\u001b[39;00m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mcegalprizm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpythontool\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m PetrelConnection\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'cegal.welltools'"]}], "source": ["# Core libraries\n", "import numpy as np\n", "import pandas as pd\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# ML libraries\n", "from xgboost import XGBRegressor\n", "from lightgbm import LGBMRegressor\n", "from catboost import CatBoostRegressor\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "# Plotting (optional)\n", "import plotly.express as px\n", "\n", "# Interactive widgets for log selection\n", "import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "\n", "# Petrel connection\n", "from cegalprizm.pythontool import PetrelConnection\n", "\n", "petrel = PetrelConnection()\n", "print(f'Connected to Petrel project: {petrel.get_current_project_name()}')"]}, {"cell_type": "markdown", "id": "3e3d2eba", "metadata": {}, "source": ["## 1 – Configure wells & logs"]}, {"cell_type": "code", "execution_count": null, "id": "b48fcdd1", "metadata": {}, "outputs": [], "source": ["# Pick all wells in project\n", "wells = [w for w in petrel.wells]\n", "print(f'Total wells detected: {len(wells)}')\n", "\n", "# Choose global well logs to work with\n", "LOG_NAMES = ['GR', 'Vp', 'RHOB', 'NPHI', 'Vs']   # edit as required\n", "\n", "# Helper function to find global well logs by name (handles both individual objects and lists)\n", "def find_global_well_logs_by_names(names):\n", "    found_logs = []\n", "    for item in petrel.global_well_logs:\n", "        # Handle both individual objects and lists of objects\n", "        if isinstance(item, list):\n", "            for obj in item:\n", "                if hasattr(obj, 'petrel_name') and obj.petrel_name in names:\n", "                    found_logs.append(obj)\n", "        else:\n", "            if hasattr(item, 'petrel_name') and item.petrel_name in names:\n", "                found_logs.append(item)\n", "    return found_logs\n", "\n", "logs = find_global_well_logs_by_names(LOG_NAMES)\n", "print(f'Using {len(logs)} global logs: {[g.petrel_name for g in logs]}')"]}, {"cell_type": "markdown", "id": "9112c3f8", "metadata": {}, "source": ["## 2 – Load log data from Petrel"]}, {"cell_type": "code", "execution_count": null, "id": "75e81177", "metadata": {}, "outputs": [], "source": ["well_data = pd.DataFrame()\n", "\n", "for w in wells:\n", "    df = w.logs_dataframe(logs)      # returns MD‑indexed DataFrame\n", "    df['WELL'] = w.petrel_name\n", "    well_data = pd.concat([well_data, df])\n", "\n", "well_data.reset_index(drop=False, inplace=True)  # MD becomes column\n", "print(f'Combined DataFrame shape: {well_data.shape}')\n", "display(well_data.head())"]}, {"cell_type": "markdown", "id": "76eebf83", "metadata": {}, "source": ["## 3 – Basic cleaning (remove obvious spikes)"]}, {"cell_type": "code", "execution_count": null, "id": "47becf17", "metadata": {}, "outputs": [], "source": ["GR_MAX = 300\n", "well_data['GR'] = np.where(well_data['GR'] <= GR_MAX, well_data['GR'], np.nan)\n", "if 'NPHI' in well_data.columns:\n", "    well_data['NPHI'] = np.where(\n", "        (well_data['NPHI'] >= 0) & (well_data['NPHI'] <= 1),\n", "        well_data['NPHI'], np.nan\n", "    )\n", "display(well_data.describe().T)"]}, {"cell_type": "markdown", "id": "c307e868", "metadata": {}, "source": ["## 4 – Coverage per log"]}, {"cell_type": "code", "execution_count": null, "id": "edb6534b", "metadata": {}, "outputs": [], "source": ["coverage = 1.0 - well_data[LOG_NAMES].isna().mean()\n", "print('\\nData coverage per log:')\n", "for log_name, cov in coverage.items():\n", "    print(f'{log_name}: {cov:.2%}')\n", "\n", "# Visualize coverage\n", "fig = px.bar(coverage, labels={'value':'Coverage'}, title='Relative data coverage')\n", "fig.show()"]}, {"cell_type": "markdown", "id": "4082c93a", "metadata": {}, "source": ["## 5 – Imputation utility"]}, {"cell_type": "code", "execution_count": null, "id": "d204d2a3", "metadata": {}, "outputs": [], "source": ["def impute_logs(df, depth_col, feature_cols, targets):\n", "    \"\"\"Return DataFrame with *_pred, *_imputed, *_error columns.\"\"\"\n", "    res = df.copy()\n", "    # Configure ML models with user's preferred hyperparameters\n", "    boosters = [\n", "        ('EXTREME BOOST REGRESSOR', XGBRegressor(\n", "            n_estimators=300,\n", "            tree_method='gpu_hist',\n", "            learning_rate=0.05,\n", "            early_stopping_rounds=100,\n", "            random_state=42\n", "        )),\n", "        ('LGBM REGRESSOR', LGBMRegressor(\n", "            device='gpu',\n", "            gpu_platform_id=1,\n", "            gpu_device_id=0,\n", "            n_estimators=300,\n", "            random_state=42\n", "        )),\n", "        ('CATBOOST REGRESSOR', CatBoostRegressor(\n", "            task_type='GPU',\n", "            early_stopping_rounds=100,\n", "            verbose=0,\n", "            random_state=42\n", "        ))\n", "    ]\n", "    feature_set = feature_cols + [depth_col]\n", "\n", "    for tgt in targets:\n", "        print(f'--- {tgt} ---')\n", "        train = res[res[tgt].notna()][feature_set + [tgt]].copy()\n", "        if train.empty:\n", "            print('No training data, skipping.')\n", "            continue\n", "        X = train.drop(columns=[tgt]).apply(lambda c: c.fillna(c.mean()), axis=0)\n", "        y = train[tgt]\n", "\n", "        Xtr, Xval, ytr, yval = train_test_split(X, y, test_size=0.25, random_state=42)\n", "        best_model, best_name, best_mae = None, None, float(\"inf\")\n", "        for name, model in boosters:\n", "            model.fit(Xtr, ytr)\n", "            mae = mean_absolute_error(yval, model.predict(Xval))\n", "            if mae < best_mae:\n", "                best_model, best_name, best_mae = model, name, mae\n", "        print(f'Chosen model: {best_name} (MAE={best_mae:.2f})')\n", "\n", "        X_full = res[feature_set].apply(lambda c: c.fillna(c.mean()), axis=0)\n", "        preds = best_model.predict(X_full)\n", "        res[f'{tgt}_pred'] = preds\n", "        res[f'{tgt}_imputed'] = res[tgt].fillna(preds)\n", "        res[f'{tgt}_error'] = np.abs(res[tgt] - preds) / res[tgt] * 100\n", "    return res"]}, {"cell_type": "markdown", "id": "e92911f8", "metadata": {}, "source": ["### Example: impute Vs where missing"]}, {"cell_type": "code", "execution_count": null, "id": "254a2a95", "metadata": {}, "outputs": [], "source": ["DEPTH_COL = 'MD'\n", "FEATURES   = [c for c in LOG_NAMES if c != 'Vs']\n", "results = impute_logs(well_data, DEPTH_COL, FEATURES, targets=['Vs'])\n", "results.head()"]}, {"cell_type": "markdown", "id": "a40f1ffa", "metadata": {}, "source": ["## 6 – (Option) Write imputed logs back to Petrel"]}, {"cell_type": "code", "execution_count": null, "id": "7ab4c50b", "metadata": {}, "outputs": [], "source": ["def write_back_to_petrel(results_df, log_name_in_results, clone_from='Vs'):\n", "    \"\"\"Clone an existing log (or first available log) and overwrite with imputed values.\"\"\"\n", "    for w in wells:\n", "        print(f'Updating {w.petrel_name}')\n", "        well_df = results_df[results_df['WELL'] == w.petrel_name].set_index('MD')\n", "        md = well_df.index.to_numpy()\n", "        values = well_df[log_name_in_results].to_numpy()\n", "\n", "        # Find a log to clone (or existing target)\n", "        target_logs = [log for log in w.logs if log.petrel_name == log_name_in_results]\n", "        if target_logs:\n", "            log_obj = target_logs[0]\n", "        else:\n", "            # Clone template log\n", "            template = [log for log in w.logs if log.petrel_name == clone_from][0]\n", "            log_obj = template.clone(w, log_name_in_results)\n", "\n", "        petrel_log_ref = petrel.well_logs[log_obj.path]\n", "        petrel_log_ref.readonly = False\n", "        petrel_log_ref.set_values(md, values)\n", "\n", "# Uncomment to execute\n", "# write_back_to_petrel(results, 'Vs_imputed')"]}], "metadata": {"kernelspec": {"display_name": "PrizmEnv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}