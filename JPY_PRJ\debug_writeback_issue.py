"""
Debug Write-Back Issue
=====================

This script helps diagnose and fix write-back issues by checking:
1. Available variables in current session
2. Data structure and content
3. Petrel connection status
4. Function availability

Run this to understand what's going wrong with your write-back.
"""

print("="*60)
print("DEBUGGING WRITE-BACK ISSUE")
print("="*60)

# Check 1: Available variables
print("\n1. CHECKING AVAILABLE VARIABLES:")
print("-" * 40)

required_vars = ['petrel', 'selected_wells', 'results', 'TARGET']
for var_name in required_vars:
    if var_name in globals():
        var_value = globals()[var_name]
        if var_name == 'petrel':
            try:
                project_name = var_value.get_current_project_name()
                print(f"✓ {var_name}: Connected to {project_name}")
            except:
                print(f"✗ {var_name}: Connection issue")
        elif var_name == 'selected_wells':
            print(f"✓ {var_name}: {len(var_value)} wells")
            for i, well in enumerate(var_value[:3]):  # Show first 3
                print(f"    {i+1}. {well.petrel_name}")
            if len(var_value) > 3:
                print(f"    ... and {len(var_value)-3} more")
        elif var_name == 'results':
            print(f"✓ {var_name}: DataFrame shape {var_value.shape}")
            print(f"    Columns: {list(var_value.columns)}")
            print(f"    Wells in data: {var_value['WELL'].unique()}")
        elif var_name == 'TARGET':
            print(f"✓ {var_name}: {var_value}")
    else:
        print(f"✗ {var_name}: NOT FOUND")

# Check 2: Function availability
print("\n2. CHECKING FUNCTION AVAILABILITY:")
print("-" * 40)

if 'write_back_to_petrel' in globals():
    print("✓ write_back_to_petrel: Available")
else:
    print("✗ write_back_to_petrel: NOT AVAILABLE")

# Check 3: Data content for VP_COREL_ML_repredicted
print("\n3. CHECKING DATA CONTENT:")
print("-" * 40)

if 'results' in globals():
    results = globals()['results']
    
    # Check for VP_COREL_ML_repredicted column
    target_columns = [col for col in results.columns if 'VP_COREL' in col and ('repredicted' in col or 'imputed' in col)]
    print(f"VP_COREL related columns: {target_columns}")
    
    if target_columns:
        for col in target_columns:
            non_null_count = results[col].notna().sum()
            total_count = len(results)
            print(f"  {col}: {non_null_count}/{total_count} non-null values")
            
            # Show sample data
            sample_data = results[results[col].notna()].head(3)
            if not sample_data.empty:
                print(f"  Sample data for {col}:")
                for _, row in sample_data.iterrows():
                    print(f"    Well: {row['WELL']}, MD: {row['MD']:.1f}, Value: {row[col]:.2f}")
    else:
        print("  No VP_COREL related columns found")
        print(f"  Available columns: {list(results.columns)}")

# Check 4: Template log availability
print("\n4. CHECKING TEMPLATE LOG AVAILABILITY:")
print("-" * 40)

if 'petrel' in globals():
    petrel = globals()['petrel']
    try:
        print("Available global well logs:")
        log_names = []
        for log in petrel.global_well_logs:
            if hasattr(log, 'petrel_name'):
                log_names.append(log.petrel_name)
        
        # Show VP-related logs
        vp_logs = [name for name in log_names if 'VP' in name.upper()]
        if vp_logs:
            print(f"  VP-related logs: {vp_logs}")
        else:
            print(f"  No VP-related logs found")
            print(f"  First 10 available logs: {log_names[:10]}")
            
    except Exception as e:
        print(f"  Error accessing global logs: {e}")

print("\n" + "="*60)
print("DIAGNOSIS COMPLETE")
print("="*60)

# Provide specific recommendations
print("\nRECOMMENDATIONS:")
print("-" * 20)

missing_vars = [var for var in required_vars if var not in globals()]
if missing_vars:
    print(f"1. Missing variables: {missing_vars}")
    print("   → Run the data loading and selection steps first")

if 'write_back_to_petrel' not in globals():
    print("2. Function not available")
    print("   → Run: exec(open('JPY_PRJ/quick_fix_writeback.py').read())")

if 'results' in globals():
    results = globals()['results']
    target_columns = [col for col in results.columns if 'VP_COREL' in col and ('repredicted' in col or 'imputed' in col)]
    if not target_columns:
        print("3. No VP_COREL prediction columns found")
        print("   → Check if ML prediction step completed successfully")

print("\nNEXT STEPS:")
print("1. Fix any missing variables or functions")
print("2. Run the working write-back solution below")
print("="*60)
