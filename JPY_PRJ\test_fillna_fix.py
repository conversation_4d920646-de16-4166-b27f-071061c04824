"""
Test script to verify the fillna fix works correctly
"""

import pandas as pd
import numpy as np

def test_fillna_fix():
    """Test the fillna fix with sample data"""
    
    print("Testing fillna fix...")
    
    # Create sample data similar to what the script would have
    np.random.seed(42)
    n_samples = 100
    
    # Create a DataFrame with some missing values
    data = {
        'MD': np.arange(1000, 1000 + n_samples),
        'AI': np.random.normal(8000, 1000, n_samples),
        'RHOB': np.random.normal(2.3, 0.2, n_samples),
        'target': np.random.normal(5000, 500, n_samples)
    }
    
    # Introduce some missing values in target
    missing_indices = np.random.choice(n_samples, size=20, replace=False)
    data['target'][missing_indices] = np.nan
    
    df = pd.DataFrame(data)
    print(f"Created test DataFrame: {df.shape}")
    print(f"Missing values in target: {df['target'].isna().sum()}")
    
    # Simulate predictions (numpy array)
    predictions = np.random.normal(5000, 500, n_samples)
    print(f"Predictions type: {type(predictions)}")
    print(f"Predictions shape: {predictions.shape}")
    
    # Test the old method (should fail)
    try:
        df_old = df.copy()
        df_old['target_imputed_old'] = df_old['target'].fillna(predictions)
        print("❌ Old method worked (unexpected)")
    except TypeError as e:
        print(f"✅ Old method failed as expected: {str(e)[:50]}...")
    
    # Test the new method (should work)
    try:
        df_new = df.copy()
        preds_series = pd.Series(predictions, index=df_new.index)
        df_new['target_imputed_new'] = df_new['target'].fillna(preds_series)
        print("✅ New method works!")
        
        # Verify the imputation worked
        missing_before = df['target'].isna().sum()
        missing_after = df_new['target_imputed_new'].isna().sum()
        print(f"Missing values before: {missing_before}")
        print(f"Missing values after: {missing_after}")
        
        if missing_after == 0:
            print("✅ All missing values successfully imputed!")
        else:
            print(f"❌ Still have {missing_after} missing values")
            
        return True
        
    except Exception as e:
        print(f"❌ New method failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_fillna_fix()
    print(f"\nTest result: {'PASS' if success else 'FAIL'}")
