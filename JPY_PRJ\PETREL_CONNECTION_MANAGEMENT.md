# Petrel Connection Management Guide

## Overview

This document explains how to properly manage Petrel connections in the Enhanced Missing Log Imputation workflow and provides general guidance for Petrel connection management in Python Tool Pro.

## Connection Management Methods

### Method 1: Direct Connection (Current Implementation)

```python
from cegalprizm.pythontool import PetrelConnection

# Create connection
petrel = PetrelConnection()
print(f'Connected to Petrel project: {petrel.get_current_project_name()}')

# ... your code here ...

# Close connection when done
petrel.close()
```

**Advantages:**
- Simple and straightforward
- Full control over connection lifecycle
- Can check connection status anytime

**Disadvantages:**
- Manual connection management required
- Risk of forgetting to close connection

### Method 2: Context Manager (Alternative)

```python
from cegalprizm.pythontool.petrelconnection import make_connection

# Automatic connection management
with make_connection() as petrel:
    print(f'Connected to Petrel project: {petrel.get_current_project_name()}')
    # ... your code here ...
    # Connection automatically closed when exiting the 'with' block
```

**Advantages:**
- Automatic connection cleanup
- Guaranteed connection closure even if errors occur
- Follows Python best practices

**Disadvantages:**
- All code must be inside the 'with' block
- Less flexibility for long-running scripts

## Enhanced Features in the Logs Imputation Script

### 1. Connection Status Checking

```python
def check_connection_status():
    """Check and display the current Petrel connection status."""
    try:
        if 'petrel' in globals():
            petrel.ping()  # Test connection
            project_name = petrel.get_current_project_name()
            print(f'✓ Petrel connection is active (Project: {project_name})')
            return True
        else:
            print('✗ No Petrel connection found.')
            return False
    except Exception as e:
        print(f'✗ Petrel connection appears to be inactive: {str(e)}')
        return False
```

### 2. Safe Connection Closing

```python
def close_petrel_connection_safely():
    """Safely close the Petrel connection with error handling."""
    try:
        if 'petrel' in globals():
            print('Closing Petrel connection...')
            petrel.close()
            print('✓ Petrel connection closed successfully.')
            return True
        else:
            print('⚠ No active Petrel connection found.')
            return False
    except Exception as e:
        print(f'✗ Error closing Petrel connection: {str(e)}')
        return False
```

### 3. Interactive Connection Management

The script provides interactive prompts for connection management:

- **During workflow completion**: Choose whether to close connection
- **Error handling**: Graceful handling of connection issues
- **Status reporting**: Clear feedback on connection state

## Best Practices

### 1. Always Close Connections

```python
# Good practice
try:
    petrel = PetrelConnection()
    # ... your work ...
finally:
    petrel.close()
```

### 2. Check Connection Before Use

```python
# Verify connection is active
try:
    petrel.ping()
    project_name = petrel.get_current_project_name()
    print(f'Working with project: {project_name}')
except Exception as e:
    print(f'Connection issue: {e}')
```

### 3. Handle Connection Errors Gracefully

```python
try:
    petrel.close()
    print('Connection closed successfully')
except Exception as e:
    print(f'Warning: Error closing connection: {e}')
    # Continue with cleanup anyway
```

## Connection States

The PetrelConnection has three states:

1. **UNOPENED**: Connection not yet established
2. **OPENED**: Active connection to Petrel
3. **CLOSED**: Connection has been closed

## Common Issues and Solutions

### Issue: "Connection already closed"
**Solution**: Check connection status before attempting operations

### Issue: "Cannot connect to Hub Server"
**Solution**: Ensure Petrel is running and Hub Server is active

### Issue: "Connection timeout"
**Solution**: Check network connectivity and Petrel responsiveness

## Integration in the Enhanced Script

The enhanced logs imputation script includes:

1. **Automatic connection management** during workflow execution
2. **Interactive cleanup options** at the end of the workflow
3. **Error handling** for connection issues
4. **Status reporting** throughout the process
5. **Multiple exit strategies** (close immediately, keep open, etc.)

## Usage Examples

### Basic Usage
```python
# The script handles this automatically
petrel = PetrelConnection()
# ... workflow execution ...
petrel.close()  # Called automatically based on user choice
```

### Manual Control
```python
# Check status anytime
check_connection_status()

# Close manually if needed
close_petrel_connection_safely()
```

### Interactive Cleanup
```python
# The script provides this automatically at the end
connection_closed = interactive_connection_cleanup()
```

## Summary

The enhanced connection management ensures:
- ✅ Proper resource cleanup
- ✅ User control over connection lifecycle
- ✅ Error handling and recovery
- ✅ Clear status reporting
- ✅ Multiple usage patterns supported

This approach provides both convenience and control while following best practices for resource management in Python Tool Pro.
