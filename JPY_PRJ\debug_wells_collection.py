"""
Debug script to test wells collection from Petrel
This script helps diagnose issues with accessing wells from the Petrel project.
"""

from cegalprizm.pythontool import PetrelConnection

def debug_wells_collection():
    """Debug function to test different ways of accessing wells"""
    
    print("="*60)
    print("DEBUGGING WELLS COLLECTION")
    print("="*60)
    
    try:
        # Connect to Petrel
        petrel = PetrelConnection()
        print(f'✓ Connected to Petrel project: {petrel.get_current_project_name()}')
        
        # Test 1: Check type of petrel.wells
        print(f"\n1. Type of petrel.wells: {type(petrel.wells)}")
        
        # Test 2: Try to get length
        try:
            wells_length = len(petrel.wells)
            print(f"2. Length of petrel.wells: {wells_length}")
        except Exception as e:
            print(f"2. Cannot get length: {str(e)}")
        
        # Test 3: Try to iterate and check first few items
        print("\n3. Iterating through petrel.wells:")
        wells_list = []
        count = 0
        
        for i, item in enumerate(petrel.wells):
            if count >= 5:  # Only check first 5 items
                break
                
            print(f"   Item {i}: Type = {type(item)}")
            
            if isinstance(item, list):
                print(f"      List with {len(item)} items")
                for j, sub_item in enumerate(item[:3]):  # Check first 3 sub-items
                    print(f"         Sub-item {j}: Type = {type(sub_item)}")
                    if hasattr(sub_item, 'petrel_name'):
                        print(f"            Name: {sub_item.petrel_name}")
                        wells_list.append(sub_item)
                    else:
                        print(f"            No petrel_name attribute")
            else:
                if hasattr(item, 'petrel_name'):
                    print(f"      Name: {item.petrel_name}")
                    wells_list.append(item)
                else:
                    print(f"      No petrel_name attribute")
                    print(f"      Available attributes: {[attr for attr in dir(item) if not attr.startswith('_')][:10]}")
            
            count += 1
        
        print(f"\n4. Total wells found: {len(wells_list)}")
        if wells_list:
            print("   Well names:")
            for well in wells_list[:10]:  # Show first 10
                print(f"      - {well.petrel_name}")
        
        # Test 4: Try alternative access methods
        print("\n5. Testing alternative access methods:")
        
        # Method 1: Direct list conversion
        try:
            wells_direct = list(petrel.wells)
            print(f"   Direct list conversion: {len(wells_direct)} items")
        except Exception as e:
            print(f"   Direct list conversion failed: {str(e)}")
        
        # Method 2: Check if it's already a list
        if isinstance(petrel.wells, list):
            print(f"   petrel.wells is already a list with {len(petrel.wells)} items")
        
        return wells_list
        
    except Exception as e:
        print(f"✗ Error connecting to Petrel or accessing wells: {str(e)}")
        return []

def debug_logs_collection():
    """Debug function to test global well logs collection"""
    
    print("\n" + "="*60)
    print("DEBUGGING GLOBAL WELL LOGS COLLECTION")
    print("="*60)
    
    try:
        petrel = PetrelConnection()
        
        print(f"1. Type of petrel.global_well_logs: {type(petrel.global_well_logs)}")
        
        # Try to get length
        try:
            logs_length = len(petrel.global_well_logs)
            print(f"2. Length of petrel.global_well_logs: {logs_length}")
        except Exception as e:
            print(f"2. Cannot get length: {str(e)}")
        
        # Iterate through logs
        print("\n3. Iterating through global well logs:")
        logs_dict = {}
        count = 0
        
        for i, log in enumerate(petrel.global_well_logs):
            if count >= 10:  # Only check first 10
                break
                
            print(f"   Log {i}: Type = {type(log)}")
            
            if isinstance(log, list):
                print(f"      List with {len(log)} items")
                for sub_log in log[:3]:  # Check first 3 sub-items
                    if hasattr(sub_log, 'petrel_name'):
                        print(f"         - {sub_log.petrel_name}")
                        logs_dict[sub_log.petrel_name] = sub_log
            else:
                if hasattr(log, 'petrel_name'):
                    print(f"      Name: {log.petrel_name}")
                    logs_dict[log.petrel_name] = log
                else:
                    print(f"      No petrel_name attribute")
            
            count += 1
        
        print(f"\n4. Total logs found: {len(logs_dict)}")
        if logs_dict:
            print("   Log names:")
            for name in sorted(list(logs_dict.keys())[:15]):  # Show first 15
                print(f"      - {name}")
        
        return logs_dict
        
    except Exception as e:
        print(f"✗ Error accessing global well logs: {str(e)}")
        return {}

if __name__ == "__main__":
    print("Starting Petrel connection debugging...")
    
    # Debug wells
    wells = debug_wells_collection()
    
    # Debug logs
    logs = debug_logs_collection()
    
    print("\n" + "="*60)
    print("DEBUGGING SUMMARY")
    print("="*60)
    print(f"Wells found: {len(wells)}")
    print(f"Logs found: {len(logs)}")
    
    if wells and logs:
        print("✓ Both wells and logs are accessible - the main script should work")
    elif not wells:
        print("✗ Wells access issue - need to fix wells collection")
    elif not logs:
        print("✗ Logs access issue - need to fix logs collection")
    else:
        print("✗ Both wells and logs have issues")
