"""
Simple test to verify wells access works
"""

from cegalprizm.pythontool import PetrelConnection

def test_wells_access():
    """Test basic wells access"""
    try:
        petrel = PetrelConnection()
        print(f'Connected to: {petrel.get_current_project_name()}')
        
        # Test the standard approach
        print('\nTesting standard approach...')
        wells = list(petrel.wells)
        well_names = [w.petrel_name for w in wells]
        
        print(f'Success! Found {len(wells)} wells')
        if wells:
            print(f'First 3 wells: {well_names[:3]}')
        
        return True
        
    except Exception as e:
        print(f'Error: {e}')
        return False

if __name__ == "__main__":
    success = test_wells_access()
    print(f'Test result: {"PASS" if success else "FAIL"}')
