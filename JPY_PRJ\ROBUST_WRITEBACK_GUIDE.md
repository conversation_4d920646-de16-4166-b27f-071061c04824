# Robust Write-Back Implementation Guide

## Overview

This guide explains how to use the robust `write_back_to_petrel` function implementation that resolves the NameError issues in the log imputation workflow.

## Problem Solved

The original issue was a **NameError** when trying to call `write_back_to_petrel` function for writing ML-predicted log data (specifically "VP_COREL_ML_repredicted") back to Petrel software.

## Solution Implemented

### 1. Enhanced Function Implementation

The robust implementation includes two main functions:

- **`write_back_log_robust`**: Core function that handles the actual Petrel API interactions
- **`write_back_to_petrel`**: User-friendly wrapper function with enhanced error handling

### 2. Key Improvements

- **Robust Error Handling**: Comprehensive exception handling with detailed debug output
- **NaN Value Cleaning**: Automatic removal of invalid data points before writing
- **Global Log Management**: Proper handling of Petrel's global well log collections
- **Template Support**: Flexible log cloning from existing templates
- **Debug Logging**: Extensive debug output for troubleshooting

## Usage Instructions

### Basic Usage

```python
# 1. Ensure you have the required imports
from cegalprizm.pythontool import PetrelConnection, Well, GlobalWellLog, WellLog, DiscreteGlobalWellLog

# 2. Establish Petrel connection
petrel = PetrelConnection()

# 3. Run the enhanced script or import the functions
from Logs_imputation_petrel_enhanced import write_back_to_petrel

# 4. Call the function with your results
write_back_to_petrel(
    results_df=results,                    # Your DataFrame with predictions
    log_name_in_results='VP_COREL_ML_repredicted',  # Column name to write back
    clone_from='VP',                       # Template log to clone from
    new_log_name='VP_ML_predicted'         # Name for the new log in Petrel
)
```

### Advanced Usage

```python
# For more control, use the robust core function directly
from Logs_imputation_petrel_enhanced import write_back_log_robust

# Find the global log template
def find_global_well_log_by_name(name):
    for item in petrel.global_well_logs:
        if isinstance(item, list):
            for obj in item:
                if hasattr(obj, 'petrel_name') and obj.petrel_name == name:
                    return obj
        elif hasattr(item, 'petrel_name') and item.petrel_name == name:
            return item
    return None

log_to_clone = find_global_well_log_by_name('VP')

# Write back to each well individually
for well in selected_wells:
    well_data = results[results['WELL'] == well.petrel_name]
    if not well_data.empty:
        md_values = well_data['MD'].values
        log_values = well_data['VP_COREL_ML_repredicted'].values
        
        # Clean NaN values
        valid_mask = ~np.isnan(md_values) & ~np.isnan(log_values)
        md_clean = md_values[valid_mask].tolist()
        values_clean = log_values[valid_mask].tolist()
        
        # Write back using robust function
        success = write_back_log_robust(
            petrel=petrel,
            log_name='VP_ML_predicted',
            log_to_clone=log_to_clone,
            well=well,
            md=md_clean,
            values=values_clean
        )
```

## Required Context Variables

The function expects these variables to be available in the global scope:

- **`petrel`**: PetrelConnection instance
- **`selected_wells`**: List of Well objects
- **`selected_target_log`**: Name of the target log (used as fallback template)

## Function Parameters

### `write_back_to_petrel(results_df, log_name_in_results, clone_from=None, new_log_name=None)`

- **`results_df`**: DataFrame containing the ML predictions
- **`log_name_in_results`**: Column name in the DataFrame to write back
- **`clone_from`**: Template log name to clone from (defaults to selected_target_log)
- **`new_log_name`**: Name for the new log in Petrel (auto-generated if None)

### `write_back_log_robust(petrel, log_name, log_to_clone, well, md, values, template=None)`

- **`petrel`**: PetrelConnection instance
- **`log_name`**: Name of the log to create/update
- **`log_to_clone`**: GlobalWellLog object to use as template
- **`well`**: Well object to write to
- **`md`**: List of measured depth values
- **`values`**: List of log values
- **`template`**: Optional template for cloning

## Error Handling

The robust implementation includes comprehensive error handling:

1. **Missing Context Variables**: Checks for required variables and provides clear error messages
2. **Data Validation**: Validates input data and removes NaN values
3. **Petrel API Errors**: Catches and reports Petrel-specific errors
4. **Template Issues**: Handles missing template logs gracefully

## Testing

Run the test script to verify the implementation:

```bash
python JPY_PRJ/test_robust_writeback.py
```

## Integration with Existing Workflows

### For VP_COREL_ML_repredicted Data

```python
# Assuming you have results DataFrame with VP_COREL_ML_repredicted column
write_back_to_petrel(
    results_df=results,
    log_name_in_results='VP_COREL_ML_repredicted',
    clone_from='VP',  # or 'VP_COREL' depending on your template log name
    new_log_name='VP_COREL_ML_repredicted'
)
```

### For Multiple Target Wells

The function automatically processes all wells in the `selected_wells` list and provides progress feedback for each well.

## Troubleshooting

### Common Issues

1. **NameError: 'write_back_to_petrel' is not defined**
   - Solution: Import the function or run the enhanced script

2. **ERROR: Console selection not completed**
   - Solution: Ensure `selected_wells` variable is available in global scope

3. **No template log found**
   - Solution: Verify the `clone_from` parameter matches an existing log name

4. **No valid data points**
   - Solution: Check that your results DataFrame contains valid (non-NaN) data

### Debug Output

The robust implementation provides extensive debug output to help troubleshoot issues:

- Data validation steps
- Petrel API interactions
- Template log searches
- Write operation results

## Performance Notes

- The function processes wells sequentially for better error isolation
- NaN values are automatically cleaned to prevent Petrel API errors
- Progress is reported for each well to track completion status

## Connection Management

Remember to properly manage your Petrel connection:

```python
# At the end of your workflow
petrel.close()
```

Or use the context manager approach for automatic cleanup:

```python
from cegalprizm.pythontool.petrelconnection import make_connection

with make_connection() as petrel:
    # Your workflow here
    write_back_to_petrel(...)
    # Connection automatically closed
```
